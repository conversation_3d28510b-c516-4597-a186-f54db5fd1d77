use std::cmp;

use serde::{Deserialize, Serialize};

/// 分页配置常量
pub const DEFAULT_PAGE_SIZE: usize = 20;
pub const MAX_PAGE_SIZE: usize = 100;
pub const MIN_PAGE_SIZE: usize = 1;

/// 分页参数结构
/// 提供标准化的分页参数处理
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParams {
    /// 分页大小
    pub size: usize,
    /// 分页偏移
    pub from: usize,
}

/// 分页信息结构
/// 用于响应中返回分页相关信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationInfo {
    /// 当前页码（从1开始）
    pub current_page: usize,
    /// 每页大小
    pub page_size: usize,
    /// 总记录数
    pub total_items: usize,
    /// 总页数
    pub total_pages: usize,
    /// 是否有下一页
    pub has_next: bool,
    /// 是否有上一页
    pub has_previous: bool,
    /// 当前页起始位置（从0开始）
    pub from: usize,
    /// 当前页结束位置（不包含）
    pub to: usize,
}

/// 排序参数结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SortParams {
    /// 排序字段
    pub field: String,
    /// 排序方向
    pub direction: SortDirection,
}

/// 排序方向枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SortDirection {
    #[serde(rename = "asc")]
    Ascending,
    #[serde(rename = "desc")]
    Descending,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            size: DEFAULT_PAGE_SIZE,
            from: 0,
        }
    }
}

impl PaginationParams {
    /// 创建新的分页参数
    pub fn new(size: usize, from: usize) -> Self {
        Self {
            size: cmp::max(MIN_PAGE_SIZE, cmp::min(size, MAX_PAGE_SIZE)),
            from,
        }
    }

    /// 从页码和页大小创建分页参数
    ///
    /// # 参数
    /// * `page` - 页码（从1开始）
    /// * `size` - 页大小
    ///
    /// # 返回
    /// * `PaginationParams` - 分页参数
    pub fn from_page(page: usize, size: usize) -> Self {
        let page = cmp::max(1, page); // 页码最小为1
        let size = cmp::max(MIN_PAGE_SIZE, cmp::min(size, MAX_PAGE_SIZE));
        let from = (page - 1) * size;

        Self { size, from }
    }

    /// 获取当前页码（从1开始）
    pub fn get_current_page(&self) -> usize {
        (self.from / self.size) + 1
    }

    /// 获取验证后的分页大小
    pub fn get_validated_size(&self) -> usize {
        cmp::max(MIN_PAGE_SIZE, cmp::min(self.size, MAX_PAGE_SIZE))
    }

    /// 验证分页参数
    pub fn validate(&self) -> Result<(), String> {
        if self.size == 0 {
            return Err("分页大小不能为0".to_string());
        }

        if self.size > MAX_PAGE_SIZE {
            return Err(format!("分页大小不能超过{}", MAX_PAGE_SIZE));
        }

        Ok(())
    }

    /// 计算分页信息
    ///
    /// # 参数
    /// * `total_items` - 总记录数
    ///
    /// # 返回
    /// * `PaginationInfo` - 分页信息
    pub fn calculate_info(&self, total_items: usize) -> PaginationInfo {
        let validated_size = self.get_validated_size();
        let current_page = self.get_current_page();
        let total_pages = if total_items == 0 {
            1
        } else {
            (total_items + validated_size - 1) / validated_size
        };

        let has_next = current_page < total_pages;
        let has_previous = current_page > 1;
        let to = cmp::min(self.from + validated_size, total_items);

        PaginationInfo {
            current_page,
            page_size: validated_size,
            total_items,
            total_pages,
            has_next,
            has_previous,
            from: self.from,
            to,
        }
    }
}

impl SortParams {
    /// 创建新的排序参数
    pub fn new(field: String, direction: SortDirection) -> Self {
        Self { field, direction }
    }

    /// 从排序字符串解析排序参数
    ///
    /// # 参数
    /// * `sort_str` - 排序字符串，格式: "field:direction" 或 "field"
    ///
    /// # 返回
    /// * `Result<SortParams, String>` - 解析结果
    pub fn from_string(sort_str: &str) -> Result<Self, String> {
        if sort_str.trim().is_empty() {
            return Err("排序字符串不能为空".to_string());
        }

        let parts: Vec<&str> = sort_str.split(':').collect();
        let field = parts[0].trim().to_string();

        if field.is_empty() {
            return Err("排序字段不能为空".to_string());
        }

        let direction = if parts.len() > 1 {
            match parts[1].trim().to_lowercase().as_str() {
                "desc" => SortDirection::Descending,
                "asc" => SortDirection::Ascending,
                _ => return Err(format!("无效的排序方向: {}", parts[1])),
            }
        } else {
            SortDirection::Ascending
        };

        Ok(Self { field, direction })
    }

    /// 转换为字符串格式
    pub fn to_string(&self) -> String {
        let direction_str = match self.direction {
            SortDirection::Ascending => "asc",
            SortDirection::Descending => "desc",
        };
        format!("{}:{}", self.field, direction_str)
    }

    /// 验证排序字段是否有效
    ///
    /// # 参数
    /// * `valid_fields` - 有效字段列表
    ///
    /// # 返回
    /// * `Result<(), String>` - 验证结果
    pub fn validate(&self, valid_fields: &[&str]) -> Result<(), String> {
        if !valid_fields.contains(&self.field.as_str()) {
            return Err(format!("无效的排序字段: {}", self.field));
        }
        Ok(())
    }
}

impl Default for SortDirection {
    fn default() -> Self {
        SortDirection::Ascending
    }
}

/// 分页工具函数
pub struct PaginationUtils;

impl PaginationUtils {
    /// 计算总页数
    ///
    /// # 参数
    /// * `total_items` - 总记录数
    /// * `page_size` - 页大小
    ///
    /// # 返回
    /// * `usize` - 总页数
    pub fn calculate_total_pages(total_items: usize, page_size: usize) -> usize {
        if total_items == 0 || page_size == 0 {
            return 1;
        }
        (total_items + page_size - 1) / page_size
    }

    /// 验证页码是否有效
    ///
    /// # 参数
    /// * `page` - 页码
    /// * `total_pages` - 总页数
    ///
    /// # 返回
    /// * `bool` - 是否有效
    pub fn is_valid_page(page: usize, total_pages: usize) -> bool {
        page >= 1 && page <= total_pages
    }

    /// 获取安全的页码（确保在有效范围内）
    ///
    /// # 参数
    /// * `page` - 请求的页码
    /// * `total_pages` - 总页数
    ///
    /// # 返回
    /// * `usize` - 安全的页码
    pub fn get_safe_page(page: usize, total_pages: usize) -> usize {
        if total_pages == 0 {
            return 1;
        }
        cmp::max(1, cmp::min(page, total_pages))
    }

    /// 计算偏移量
    ///
    /// # 参数
    /// * `page` - 页码（从1开始）
    /// * `page_size` - 页大小
    ///
    /// # 返回
    /// * `usize` - 偏移量
    pub fn calculate_offset(page: usize, page_size: usize) -> usize {
        let safe_page = cmp::max(1, page);
        (safe_page - 1) * page_size
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pagination_params_creation() {
        let params = PaginationParams::new(10, 5);
        assert_eq!(params.size, 10);
        assert_eq!(params.from, 5);
    }

    #[test]
    fn test_pagination_params_size_limits() {
        // 测试超过最大值
        let params = PaginationParams::new(200, 0);
        assert_eq!(params.size, MAX_PAGE_SIZE);

        // 测试小于最小值
        let params = PaginationParams::new(0, 0);
        assert_eq!(params.size, MIN_PAGE_SIZE);
    }

    #[test]
    fn test_pagination_from_page() {
        let params = PaginationParams::from_page(3, 10);
        assert_eq!(params.size, 10);
        assert_eq!(params.from, 20); // (3-1) * 10

        // 测试页码为0的情况
        let params = PaginationParams::from_page(0, 10);
        assert_eq!(params.from, 0); // 应该被调整为页码1
    }

    #[test]
    fn test_pagination_info_calculation() {
        let params = PaginationParams::new(10, 15);
        let info = params.calculate_info(100);

        assert_eq!(info.current_page, 2); // (15 / 10) + 1
        assert_eq!(info.page_size, 10);
        assert_eq!(info.total_items, 100);
        assert_eq!(info.total_pages, 10); // 100 / 10
        assert!(info.has_next);
        assert!(info.has_previous);
        assert_eq!(info.from, 15);
        assert_eq!(info.to, 25);
    }

    #[test]
    fn test_pagination_info_edge_cases() {
        // 测试空结果集
        let params = PaginationParams::new(10, 0);
        let info = params.calculate_info(0);
        assert_eq!(info.total_pages, 1);
        assert!(!info.has_next);
        assert!(!info.has_previous);

        // 测试最后一页
        let params = PaginationParams::new(10, 90);
        let info = params.calculate_info(95);
        assert_eq!(info.current_page, 10);
        assert_eq!(info.total_pages, 10);
        assert!(!info.has_next);
        assert!(info.has_previous);
        assert_eq!(info.to, 95); // 不超过总数
    }

    #[test]
    fn test_sort_params_from_string() {
        // 测试完整格式
        let sort = SortParams::from_string("name:desc").unwrap();
        assert_eq!(sort.field, "name");
        assert_eq!(sort.direction, SortDirection::Descending);

        // 测试简单格式
        let sort = SortParams::from_string("created").unwrap();
        assert_eq!(sort.field, "created");
        assert_eq!(sort.direction, SortDirection::Ascending);

        // 测试无效格式
        assert!(SortParams::from_string("").is_err());
        assert!(SortParams::from_string("field:invalid").is_err());
    }

    #[test]
    fn test_sort_params_to_string() {
        let sort = SortParams::new("name".to_string(), SortDirection::Descending);
        assert_eq!(sort.to_string(), "name:desc");

        let sort = SortParams::new("created".to_string(), SortDirection::Ascending);
        assert_eq!(sort.to_string(), "created:asc");
    }

    #[test]
    fn test_sort_params_validation() {
        let sort = SortParams::new("name".to_string(), SortDirection::Ascending);
        let valid_fields = &["name", "created", "updated"];

        assert!(sort.validate(valid_fields).is_ok());

        let invalid_sort = SortParams::new("invalid_field".to_string(), SortDirection::Ascending);
        assert!(invalid_sort.validate(valid_fields).is_err());
    }

    #[test]
    fn test_pagination_utils() {
        // 测试总页数计算
        assert_eq!(PaginationUtils::calculate_total_pages(100, 10), 10);
        assert_eq!(PaginationUtils::calculate_total_pages(95, 10), 10);
        assert_eq!(PaginationUtils::calculate_total_pages(0, 10), 1);

        // 测试页码验证
        assert!(PaginationUtils::is_valid_page(5, 10));
        assert!(!PaginationUtils::is_valid_page(0, 10));
        assert!(!PaginationUtils::is_valid_page(11, 10));

        // 测试安全页码
        assert_eq!(PaginationUtils::get_safe_page(0, 10), 1);
        assert_eq!(PaginationUtils::get_safe_page(15, 10), 10);
        assert_eq!(PaginationUtils::get_safe_page(5, 10), 5);

        // 测试偏移量计算
        assert_eq!(PaginationUtils::calculate_offset(1, 10), 0);
        assert_eq!(PaginationUtils::calculate_offset(3, 10), 20);
    }

    #[test]
    fn test_pagination_params_validation() {
        let valid_params = PaginationParams::new(20, 0);
        assert!(valid_params.validate().is_ok());

        let invalid_params = PaginationParams { size: 0, from: 0 };
        assert!(invalid_params.validate().is_err());

        let oversized_params = PaginationParams { size: 200, from: 0 };
        assert!(oversized_params.validate().is_err());
    }

    #[test]
    fn test_default_implementations() {
        let default_params = PaginationParams::default();
        assert_eq!(default_params.size, DEFAULT_PAGE_SIZE);
        assert_eq!(default_params.from, 0);

        let default_direction = SortDirection::default();
        assert_eq!(default_direction, SortDirection::Ascending);
    }
}
