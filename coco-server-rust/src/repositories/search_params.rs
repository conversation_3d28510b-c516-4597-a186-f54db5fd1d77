use std::collections::HashMap;

use serde::{Deserialize, Serialize};

/// 默认分页大小
fn default_size() -> usize {
    20
}

/// 默认分页偏移
fn default_from() -> usize {
    0
}

/// 搜索参数结构
/// 用于标准化所有搜索相关的参数，支持分页、排序、过滤和文本搜索
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SearchParams {
    /// 分页大小，默认20，最大100
    #[serde(default = "default_size")]
    pub size: usize,

    /// 分页偏移，默认0
    #[serde(default = "default_from")]
    pub from: usize,

    /// 排序字段，格式: "field:order"，如 "created:desc"
    #[serde(default)]
    pub sort: String,

    /// 简单文本搜索，在name、description、api_type字段中搜索
    #[serde(default)]
    pub q: String,

    /// 过滤条件，支持enabled、builtin、api_type等字段
    #[serde(flatten)]
    pub filters: HashMap<String, serde_json::Value>,
}

impl SearchParams {
    /// 创建新的搜索参数
    pub fn new() -> Self {
        Self {
            size: default_size(),
            from: default_from(),
            sort: String::new(),
            q: String::new(),
            filters: HashMap::new(),
        }
    }

    /// 设置查询字符串
    pub fn with_query(mut self, q: String) -> Self {
        self.q = q;
        self
    }

    /// 设置分页参数
    pub fn with_pagination(mut self, size: usize, from: usize) -> Self {
        self.size = size;
        self.from = from;
        self
    }

    /// 设置排序
    pub fn with_sort(mut self, sort: String) -> Self {
        self.sort = sort;
        self
    }

    /// 添加过滤条件
    pub fn with_filter(mut self, key: String, value: serde_json::Value) -> Self {
        self.filters.insert(key, value);
        self
    }

    /// 获取分页大小，确保在合理范围内
    pub fn get_size(&self) -> usize {
        if self.size == 0 {
            default_size()
        } else {
            self.size.min(100) // 最大100
        }
    }

    /// 获取分页偏移
    pub fn get_from(&self) -> usize {
        self.from
    }

    /// 检查是否有文本搜索
    pub fn has_text_search(&self) -> bool {
        !self.q.trim().is_empty()
    }

    /// 检查是否有过滤条件
    pub fn has_filters(&self) -> bool {
        !self.filters.is_empty()
    }

    /// 检查是否有排序
    pub fn has_sort(&self) -> bool {
        !self.sort.trim().is_empty()
    }

    /// 验证搜索参数的有效性
    pub fn validate(&self) -> Result<(), String> {
        // 验证分页参数
        if self.size > 100 {
            return Err("分页大小不能超过100".to_string());
        }

        // 验证排序格式
        if self.has_sort() {
            let parts: Vec<&str> = self.sort.split(':').collect();
            if parts.len() > 2 {
                return Err("排序格式无效，应为 'field' 或 'field:order'".to_string());
            }

            if parts.len() == 2 {
                let order = parts[1].to_lowercase();
                if order != "asc" && order != "desc" {
                    return Err("排序方向必须是 'asc' 或 'desc'".to_string());
                }
            }
        }

        // 验证过滤条件
        for (key, value) in &self.filters {
            match key.as_str() {
                "enabled" | "builtin" => {
                    if !value.is_boolean() {
                        return Err(format!("过滤条件 '{}' 必须是布尔值", key));
                    }
                }
                "api_type" => {
                    if !value.is_string() {
                        return Err("过滤条件 'api_type' 必须是字符串".to_string());
                    }
                }
                _ => {
                    // 允许其他过滤条件，但记录警告
                    tracing::warn!("未知的过滤条件: {}", key);
                }
            }
        }

        Ok(())
    }
}

impl Default for SearchParams {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use serde_json::json;

    use super::*;

    #[test]
    fn test_search_params_creation() {
        let params = SearchParams::new();
        assert_eq!(params.size, 20);
        assert_eq!(params.from, 0);
        assert!(params.sort.is_empty());
        assert!(params.q.is_empty());
        assert!(params.filters.is_empty());
    }

    #[test]
    fn test_search_params_builder() {
        let params = SearchParams::new()
            .with_query("test".to_string())
            .with_pagination(10, 5)
            .with_sort("name:asc".to_string())
            .with_filter("enabled".to_string(), json!(true));

        assert_eq!(params.q, "test");
        assert_eq!(params.get_size(), 10);
        assert_eq!(params.get_from(), 5);
        assert_eq!(params.sort, "name:asc");
        assert!(params.has_text_search());
        assert!(params.has_filters());
        assert!(params.has_sort());
    }

    #[test]
    fn test_search_params_validation() {
        // 有效参数
        let valid_params = SearchParams::new()
            .with_pagination(20, 0)
            .with_sort("created:desc".to_string())
            .with_filter("enabled".to_string(), json!(true));
        assert!(valid_params.validate().is_ok());

        // 无效分页大小
        let invalid_size = SearchParams::new().with_pagination(200, 0);
        assert!(invalid_size.validate().is_err());

        // 无效排序格式
        let invalid_sort = SearchParams::new().with_sort("field:invalid:order".to_string());
        assert!(invalid_sort.validate().is_err());

        // 无效排序方向
        let invalid_order = SearchParams::new().with_sort("field:invalid".to_string());
        assert!(invalid_order.validate().is_err());

        // 无效过滤条件类型
        let invalid_filter =
            SearchParams::new().with_filter("enabled".to_string(), json!("not_boolean"));
        assert!(invalid_filter.validate().is_err());
    }

    #[test]
    fn test_size_limits() {
        let params = SearchParams::new().with_pagination(150, 0);
        assert_eq!(params.get_size(), 100); // 应该被限制为100

        let params = SearchParams::new().with_pagination(0, 0);
        assert_eq!(params.get_size(), 20); // 应该使用默认值
    }

    #[test]
    fn test_serde_serialization() {
        let params = SearchParams::new()
            .with_query("test".to_string())
            .with_pagination(10, 5)
            .with_sort("name:asc".to_string())
            .with_filter("enabled".to_string(), json!(true));

        // 测试序列化
        let json_str = serde_json::to_string(&params).unwrap();
        assert!(json_str.contains("\"q\":\"test\""));
        assert!(json_str.contains("\"size\":10"));
        assert!(json_str.contains("\"enabled\":true"));

        // 测试反序列化
        let deserialized: SearchParams = serde_json::from_str(&json_str).unwrap();
        assert_eq!(deserialized.q, "test");
        assert_eq!(deserialized.size, 10);
        assert_eq!(deserialized.from, 5);
    }
}
