use super::search_params::SearchParams;
use std::collections::HashMap;
use tracing::warn;

/// SurrealDB查询构建器
/// 负责将搜索参数转换为SurrealDB查询语句
pub struct QueryBuilder;

/// 查询构建结果
/// 包含SQL语句和绑定参数
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct QueryResult {
    /// SQL查询语句
    pub sql: String,
    /// 绑定参数
    pub params: Vec<surrealdb::sql::Value>,
}

impl QueryBuilder {
    /// 创建新的查询构建器实例
    pub fn new() -> Self {
        Self
    }

    /// 构建完整的搜索查询
    /// 
    /// # 参数
    /// * `params` - 搜索参数
    /// 
    /// # 返回
    /// * `QueryResult` - 包含SQL和参数的查询结果
    pub fn build_search_query(&self, params: &SearchParams) -> QueryResult {
        let mut sql = "SELECT * FROM model_provider".to_string();
        let mut bind_params = Vec::new();

        // 构建WHERE子句
        let where_clause = self.build_where_clause(params, &mut bind_params);
        if !where_clause.is_empty() {
            sql.push_str(&format!(" WHERE {}", where_clause));
        }

        // 构建ORDER BY子句
        let sort_clause = self.build_sort_clause(&params.sort);
        sql.push_str(&sort_clause);

        // 构建LIMIT子句
        let pagination_clause = self.build_pagination_clause(params.get_size(), params.get_from());
        sql.push_str(&pagination_clause);

        QueryResult {
            sql,
            params: bind_params,
        }
    }

    /// 构建WHERE子句，包含文本搜索和过滤条件
    fn build_where_clause(&self, params: &SearchParams, bind_params: &mut Vec<surrealdb::sql::Value>) -> String {
        let mut conditions = Vec::new();

        // 添加文本搜索条件
        if params.has_text_search() {
            let text_condition = self.build_text_search_condition(&params.q, bind_params);
            conditions.push(text_condition);
        }

        // 添加过滤条件
        if params.has_filters() {
            let filter_conditions = self.build_filter_conditions(&params.filters, bind_params);
            conditions.extend(filter_conditions);
        }

        conditions.join(" AND ")
    }

    /// 构建文本搜索条件
    /// 在name、description、api_type字段中搜索
    fn build_text_search_condition(&self, query: &str, bind_params: &mut Vec<surrealdb::sql::Value>) -> String {
        let param_index = bind_params.len();
        bind_params.push(surrealdb::sql::Value::from(query.to_string()));
        
        format!(
            "(name CONTAINS ${} OR description CONTAINS ${} OR api_type CONTAINS ${})",
            param_index, param_index, param_index
        )
    }

    /// 构建过滤条件
    /// 
    /// # 参数
    /// * `filters` - 过滤条件映射
    /// * `bind_params` - 绑定参数列表（可变引用）
    /// 
    /// # 返回
    /// * `Vec<String>` - 过滤条件字符串列表
    pub fn build_filter_conditions(
        &self, 
        filters: &HashMap<String, serde_json::Value>,
        bind_params: &mut Vec<surrealdb::sql::Value>
    ) -> Vec<String> {
        let mut conditions = Vec::new();

        for (key, value) in filters {
            match key.as_str() {
                "enabled" => {
                    if let Some(enabled) = value.as_bool() {
                        let param_index = bind_params.len();
                        bind_params.push(surrealdb::sql::Value::from(enabled));
                        conditions.push(format!("enabled = ${}", param_index));
                    }
                }
                "builtin" => {
                    if let Some(builtin) = value.as_bool() {
                        let param_index = bind_params.len();
                        bind_params.push(surrealdb::sql::Value::from(builtin));
                        conditions.push(format!("builtin = ${}", param_index));
                    }
                }
                "api_type" => {
                    if let Some(api_type) = value.as_str() {
                        let param_index = bind_params.len();
                        bind_params.push(surrealdb::sql::Value::from(api_type.to_string()));
                        conditions.push(format!("api_type = ${}", param_index));
                    }
                }
                _ => {
                    warn!("未知的过滤条件: {}", key);
                }
            }
        }

        conditions
    }

    /// 构建排序子句
    /// 
    /// # 参数
    /// * `sort` - 排序字符串，格式: "field:order" 或 "field"
    /// 
    /// # 返回
    /// * `String` - ORDER BY子句
    pub fn build_sort_clause(&self, sort: &str) -> String {
        if sort.trim().is_empty() {
            // 默认按创建时间降序排序
            return " ORDER BY created DESC".to_string();
        }

        // 解析排序字符串
        let parts: Vec<&str> = sort.split(':').collect();
        let field = parts[0].trim();
        
        // 验证字段名（防止SQL注入）
        if !self.is_valid_sort_field(field) {
            warn!("无效的排序字段: {}, 使用默认排序", field);
            return " ORDER BY created DESC".to_string();
        }

        let direction = if parts.len() > 1 {
            match parts[1].trim().to_lowercase().as_str() {
                "desc" => "DESC",
                "asc" => "ASC",
                _ => {
                    warn!("无效的排序方向: {}, 使用ASC", parts[1]);
                    "ASC"
                }
            }
        } else {
            "ASC"
        };

        format!(" ORDER BY {} {}", field, direction)
    }

    /// 构建分页子句
    /// 
    /// # 参数
    /// * `size` - 分页大小
    /// * `from` - 分页偏移
    /// 
    /// # 返回
    /// * `String` - LIMIT子句
    pub fn build_pagination_clause(&self, size: usize, from: usize) -> String {
        format!(" LIMIT {} START {}", size, from)
    }

    /// 验证排序字段是否有效
    /// 防止SQL注入攻击
    fn is_valid_sort_field(&self, field: &str) -> bool {
        const VALID_FIELDS: &[&str] = &[
            "id", "name", "api_type", "base_url", "enabled", 
            "builtin", "description", "created", "updated"
        ];
        
        VALID_FIELDS.contains(&field)
    }

    /// 构建计数查询，用于获取总记录数
    /// 
    /// # 参数
    /// * `params` - 搜索参数
    /// 
    /// # 返回
    /// * `QueryResult` - 计数查询结果
    pub fn build_count_query(&self, params: &SearchParams) -> QueryResult {
        let mut sql = "SELECT count() FROM model_provider".to_string();
        let mut bind_params = Vec::new();

        // 构建WHERE子句（与搜索查询相同的条件）
        let where_clause = self.build_where_clause(params, &mut bind_params);
        if !where_clause.is_empty() {
            sql.push_str(&format!(" WHERE {}", where_clause));
        }

        QueryResult {
            sql,
            params: bind_params,
        }
    }
}

impl Default for QueryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_basic_search_query() {
        let builder = QueryBuilder::new();
        let params = SearchParams::new();
        
        let result = builder.build_search_query(&params);
        
        assert!(result.sql.contains("SELECT * FROM model_provider"));
        assert!(result.sql.contains("ORDER BY created DESC"));
        assert!(result.sql.contains("LIMIT 20 START 0"));
        assert!(result.params.is_empty());
    }

    #[test]
    fn test_text_search_query() {
        let builder = QueryBuilder::new();
        let params = SearchParams::new().with_query("deepseek".to_string());
        
        let result = builder.build_search_query(&params);
        
        assert!(result.sql.contains("WHERE"));
        assert!(result.sql.contains("CONTAINS"));
        assert_eq!(result.params.len(), 1);
    }

    #[test]
    fn test_filter_conditions() {
        let builder = QueryBuilder::new();
        let mut bind_params = Vec::new();
        let mut filters = HashMap::new();
        filters.insert("enabled".to_string(), json!(true));
        filters.insert("api_type".to_string(), json!("openai"));
        
        let conditions = builder.build_filter_conditions(&filters, &mut bind_params);
        
        assert_eq!(conditions.len(), 2);
        assert_eq!(bind_params.len(), 2);
        assert!(conditions.iter().any(|c| c.contains("enabled")));
        assert!(conditions.iter().any(|c| c.contains("api_type")));
    }

    #[test]
    fn test_sort_clause() {
        let builder = QueryBuilder::new();
        
        // 测试默认排序
        let sort_clause = builder.build_sort_clause("");
        assert_eq!(sort_clause, " ORDER BY created DESC");
        
        // 测试指定字段排序
        let sort_clause = builder.build_sort_clause("name:asc");
        assert_eq!(sort_clause, " ORDER BY name ASC");
        
        // 测试无效字段
        let sort_clause = builder.build_sort_clause("invalid_field:desc");
        assert_eq!(sort_clause, " ORDER BY created DESC");
    }

    #[test]
    fn test_pagination_clause() {
        let builder = QueryBuilder::new();
        
        let pagination = builder.build_pagination_clause(10, 5);
        assert_eq!(pagination, " LIMIT 10 START 5");
    }

    #[test]
    fn test_count_query() {
        let builder = QueryBuilder::new();
        let params = SearchParams::new()
            .with_query("test".to_string())
            .with_filter("enabled".to_string(), json!(true));
        
        let result = builder.build_count_query(&params);
        
        assert!(result.sql.contains("SELECT count() FROM model_provider"));
        assert!(result.sql.contains("WHERE"));
        assert!(!result.sql.contains("ORDER BY"));
        assert!(!result.sql.contains("LIMIT"));
    }

    #[test]
    fn test_complex_query() {
        let builder = QueryBuilder::new();
        let params = SearchParams::new()
            .with_query("deepseek".to_string())
            .with_pagination(10, 20)
            .with_sort("name:desc".to_string())
            .with_filter("enabled".to_string(), json!(true))
            .with_filter("builtin".to_string(), json!(false));
        
        let result = builder.build_search_query(&params);
        
        assert!(result.sql.contains("WHERE"));
        assert!(result.sql.contains("CONTAINS"));
        assert!(result.sql.contains("enabled ="));
        assert!(result.sql.contains("builtin ="));
        assert!(result.sql.contains("ORDER BY name DESC"));
        assert!(result.sql.contains("LIMIT 10 START 20"));
        assert_eq!(result.params.len(), 3); // query + enabled + builtin
    }
}
