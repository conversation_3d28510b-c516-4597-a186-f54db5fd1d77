use std::sync::Arc;

use axum::{
    extract::{Path, Query, State},
    http::{HeaderMap, StatusCode},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use tracing::{error, info, warn};

use crate::{
    config::config_manager::Config<PERSON>anager,
    error::error::CocoError,
    handlers::response_formatter::{
        CreateModelProviderResponse, DeleteModelProviderResponse, GetModelProviderResponse,
        ResponseFormatter, SearchModelProviderResponse, UpdateModelProviderResponse,
    },
    models::model_provider::{CreateModelProviderRequest, UpdateModelProviderRequest},
    repositories::{
        cache_manager::CacheManager, model_provider_repo::SurrealModelProviderRepository,
        search_params::SearchParams,
    },
    services::{
        cache_service::CacheService,
        model_provider_service::{ModelProviderService, ModelProviderServiceTrait},
        validation_service::ValidationService,
    },
};

/// 模型提供商API处理器
///
/// 处理模型提供商相关的HTTP请求
pub struct ModelProviderHandler {
    service: Arc<ModelProviderService>,
}

impl ModelProviderHandler {
    /// 创建新的模型提供商处理器
    ///
    /// # 参数
    /// * `service` - 模型提供商服务
    pub fn new(service: Arc<ModelProviderService>) -> Self {
        Self { service }
    }
}

/// 创建模型提供商处理器
///
/// 处理 POST /model_provider/ 请求
pub async fn create_model_provider_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(request): Json<CreateModelProviderRequest>,
) -> Result<(StatusCode, Json<CreateModelProviderResponse>), (StatusCode, Json<serde_json::Value>)>
{
    info!("处理创建模型提供商请求: name={}", request.name);

    // 获取或创建模型提供商服务
    let service = get_or_create_service().await;

    // 调用业务服务创建模型提供商
    match service.create(request).await {
        Ok(id) => {
            info!("模型提供商创建成功: id={}", id);
            let response = ResponseFormatter::format_create_success(id);
            Ok((StatusCode::CREATED, Json(response)))
        }
        Err(e) => {
            error!("创建模型提供商失败: {}", e);
            let (status_code, error_response) = map_error_to_response(&e);
            Err((status_code, Json(error_response)))
        }
    }
}

/// 获取模型提供商处理器
///
/// 处理 GET /model_provider/:id 请求
pub async fn get_model_provider_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Path(id): Path<String>,
) -> Result<Json<GetModelProviderResponse>, (StatusCode, Json<serde_json::Value>)> {
    info!("处理获取模型提供商请求: id={}", id);

    // 获取或创建模型提供商服务
    let service = get_or_create_service().await;

    // 调用业务服务获取模型提供商
    match service.get_by_id(&id).await {
        Ok(Some(provider)) => {
            info!("模型提供商获取成功: id={}", id);
            let response = ResponseFormatter::format_get_success(provider);
            Ok(Json(response))
        }
        Ok(None) => {
            warn!("模型提供商未找到: id={}", id);
            let response = ResponseFormatter::format_get_not_found(id);
            Ok(Json(response))
        }
        Err(e) => {
            error!("获取模型提供商失败: id={}, error={}", id, e);
            let (status_code, error_response) = map_error_to_response(&e);
            Err((status_code, Json(error_response)))
        }
    }
}

/// 更新模型提供商处理器
///
/// 处理 PUT /model_provider/:id 请求
pub async fn update_model_provider_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Path(id): Path<String>,
    Json(request): Json<UpdateModelProviderRequest>,
) -> Result<(StatusCode, Json<UpdateModelProviderResponse>), (StatusCode, Json<serde_json::Value>)>
{
    info!("处理更新模型提供商请求: id={}", id);

    // 获取或创建模型提供商服务
    let service = get_or_create_service().await;

    // 调用业务服务更新模型提供商
    match service.update(&id, request).await {
        Ok(()) => {
            info!("模型提供商更新成功: id={}", id);
            let response = ResponseFormatter::format_update_success(id);
            Ok((StatusCode::OK, Json(response)))
        }
        Err(e) => {
            error!("更新模型提供商失败: id={}, error={}", id, e);
            let (status_code, error_response) = map_error_to_response(&e);
            Err((status_code, Json(error_response)))
        }
    }
}

/// 删除模型提供商处理器
///
/// 处理 DELETE /model_provider/:id 请求
pub async fn delete_model_provider_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<DeleteModelProviderResponse>), (StatusCode, Json<serde_json::Value>)>
{
    info!("处理删除模型提供商请求: id={}", id);

    // 获取或创建模型提供商服务
    let service = get_or_create_service().await;

    // 调用业务服务删除模型提供商
    match service.delete(&id).await {
        Ok(()) => {
            info!("模型提供商删除成功: id={}", id);
            let response = ResponseFormatter::format_delete_success(id);
            Ok((StatusCode::OK, Json(response)))
        }
        Err(e) => {
            error!("删除模型提供商失败: id={}, error={}", id, e);
            let (status_code, error_response) = map_error_to_response(&e);
            Err((status_code, Json(error_response)))
        }
    }
}

/// 获取或创建模型提供商服务
///
/// # 参数
/// * `app_state` - 应用状态
async fn get_or_create_service() -> Arc<ModelProviderService> {
    // 创建依赖服务
    let db = Arc::new(crate::database::DB.clone());

    // 创建Repository
    let repository = Arc::new(SurrealModelProviderRepository::new(db));

    // 创建CacheService
    let cache_manager = Arc::new(CacheManager::new(1800)); // 30分钟TTL
    let cache_service = Arc::new(CacheService::new(cache_manager));

    // 创建ValidationService
    let validation_service = Arc::new(ValidationService::new());

    // 创建ModelProviderService
    Arc::new(ModelProviderService::new(
        repository,
        cache_service,
        validation_service,
    ))
}

/// 将业务错误映射为HTTP响应
///
/// # 参数
/// * `error` - 业务错误
fn map_error_to_response(error: &CocoError) -> (StatusCode, serde_json::Value) {
    match error {
        CocoError::ModelProviderValidation(msg) => {
            let response = ResponseFormatter::format_validation_error(msg);
            (
                StatusCode::BAD_REQUEST,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::ModelProviderNotFound(msg) => {
            let response = ResponseFormatter::format_not_found_error(msg);
            (
                StatusCode::NOT_FOUND,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::BuiltinProviderProtection(msg) => {
            let response = ResponseFormatter::format_permission_error(msg);
            (
                StatusCode::FORBIDDEN,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::Repository(msg) => {
            let response =
                ResponseFormatter::format_internal_error(&format!("数据库错误: {}", msg));
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::Database(msg) => {
            let response =
                ResponseFormatter::format_internal_error(&format!("数据库连接错误: {}", msg));
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::JsonError(e) => {
            let response = ResponseFormatter::format_internal_error(&format!("序列化错误: {}", e));
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
        _ => {
            let response = ResponseFormatter::format_internal_error("内部服务器错误");
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
    }
}

/// POST搜索请求结构体
/// 用于解析POST /model_provider/_search的JSON请求体
#[derive(Debug, Deserialize)]
pub struct SearchRequest {
    /// 分页大小
    pub size: Option<usize>,
    /// 分页偏移
    pub from: Option<usize>,
    /// 排序字段
    pub sort: Option<String>,
    /// 查询条件
    pub query: Option<SearchQuery>,
}

/// 搜索查询条件
#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    /// 文本搜索
    pub text: Option<String>,
    /// 过滤条件
    pub filters: Option<std::collections::HashMap<String, serde_json::Value>>,
}

/// GET方式搜索处理器
///
/// 处理 GET /model_provider/_search 请求
pub async fn search_model_provider_get_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Query(query_params): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<SearchModelProviderResponse>, (StatusCode, Json<serde_json::Value>)> {
    info!("处理GET搜索模型提供商请求: {:?}", query_params);

    // 将查询参数转换为SearchParams
    let mut search_params = SearchParams::new();

    // 解析分页参数
    if let Some(size_str) = query_params.get("size") {
        if let Ok(size) = size_str.parse::<usize>() {
            search_params.size = size;
        }
    }

    if let Some(from_str) = query_params.get("from") {
        if let Ok(from) = from_str.parse::<usize>() {
            search_params.from = from;
        }
    }

    // 解析排序参数
    if let Some(sort) = query_params.get("sort") {
        search_params.sort = sort.clone();
    }

    // 解析文本搜索参数
    if let Some(q) = query_params.get("q") {
        search_params.q = q.clone();
    }

    // 解析过滤参数
    for (key, value) in &query_params {
        match key.as_str() {
            "enabled" => {
                if let Ok(enabled) = value.parse::<bool>() {
                    search_params
                        .filters
                        .insert(key.clone(), serde_json::Value::Bool(enabled));
                }
            }
            "builtin" => {
                if let Ok(builtin) = value.parse::<bool>() {
                    search_params
                        .filters
                        .insert(key.clone(), serde_json::Value::Bool(builtin));
                }
            }
            "api_type" => {
                search_params
                    .filters
                    .insert(key.clone(), serde_json::Value::String(value.clone()));
            }
            _ => {
                // 忽略其他参数
            }
        }
    }

    // 执行搜索
    execute_search(search_params).await
}

/// POST方式搜索处理器
///
/// 处理 POST /model_provider/_search 请求
pub async fn search_model_provider_post_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(request): Json<SearchRequest>,
) -> Result<Json<SearchModelProviderResponse>, (StatusCode, Json<serde_json::Value>)> {
    info!("处理POST搜索模型提供商请求: {:?}", request);

    // 将SearchRequest转换为SearchParams
    let mut search_params = SearchParams::new();

    // 设置分页参数
    if let Some(size) = request.size {
        search_params.size = size;
    }
    if let Some(from) = request.from {
        search_params.from = from;
    }

    // 设置排序参数
    if let Some(sort) = request.sort {
        search_params.sort = sort;
    }

    // 设置查询条件
    if let Some(query) = request.query {
        if let Some(text) = query.text {
            search_params.q = text;
        }
        if let Some(filters) = query.filters {
            search_params.filters = filters;
        }
    }

    // 执行搜索
    execute_search(search_params).await
}

/// OPTIONS方式搜索处理器
///
/// 处理 OPTIONS /model_provider/_search 请求（CORS预检）
pub async fn search_model_provider_options_handler() -> Result<(StatusCode, HeaderMap), StatusCode>
{
    info!("处理OPTIONS搜索模型提供商请求");

    let mut headers = HeaderMap::new();
    headers.insert("Access-Control-Allow-Origin", "*".parse().unwrap());
    headers.insert(
        "Access-Control-Allow-Methods",
        "GET, POST, OPTIONS".parse().unwrap(),
    );
    headers.insert(
        "Access-Control-Allow-Headers",
        "Content-Type, Authorization, X-API-TOKEN".parse().unwrap(),
    );
    headers.insert("Access-Control-Max-Age", "86400".parse().unwrap());

    Ok((StatusCode::OK, headers))
}

/// 执行搜索的通用函数
async fn execute_search(
    search_params: SearchParams,
) -> Result<Json<SearchModelProviderResponse>, (StatusCode, Json<serde_json::Value>)> {
    // 验证搜索参数
    if let Err(validation_error) = search_params.validate() {
        error!("搜索参数验证失败: {}", validation_error);
        let error_response = ResponseFormatter::format_validation_error(&validation_error);
        return Err((
            StatusCode::BAD_REQUEST,
            Json(serde_json::to_value(error_response).unwrap()),
        ));
    }

    // 将SearchParams转换为SearchQuery（为了兼容现有的service接口）
    let search_query = convert_search_params_to_query(search_params);

    // 获取或创建模型提供商服务
    let service = get_or_create_service().await;

    // 调用业务服务执行搜索
    match service.search(search_query).await {
        Ok(search_response) => {
            info!("搜索完成: 找到 {} 条记录", search_response.hits.hits.len());

            // 将Repository层的SearchResponse转换为API响应格式
            let providers: Vec<crate::models::model_provider::ModelProvider> = search_response
                .hits
                .hits
                .into_iter()
                .map(|hit| hit.source)
                .collect();

            let api_response = ResponseFormatter::format_search_success(
                providers,
                search_response.hits.total.value,
                search_response.took,
            );

            Ok(Json(api_response))
        }
        Err(e) => {
            error!("搜索模型提供商失败: {}", e);
            let (status_code, error_response) = map_error_to_response(&e);
            Err((status_code, Json(error_response)))
        }
    }
}

/// 将SearchParams转换为SearchQuery
fn convert_search_params_to_query(
    params: SearchParams,
) -> crate::repositories::model_provider_repo::SearchQuery {
    use crate::repositories::model_provider_repo::SearchQuery;

    let mut query = SearchQuery::new().with_pagination(params.get_size(), params.get_from());

    if !params.q.is_empty() {
        query = query.with_query(params.q);
    }

    if !params.sort.is_empty() {
        query = query.with_sort(params.sort);
    }

    if !params.filters.is_empty() {
        for (key, value) in params.filters {
            query = query.with_filter(key, value);
        }
    }

    query
}

#[cfg(test)]
mod tests {
    use axum::http::StatusCode;

    use super::*;
    use crate::models::model_provider::CreateModelProviderRequest;

    fn create_test_request() -> CreateModelProviderRequest {
        CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-api-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        }
    }

    #[test]
    fn test_map_error_to_response() {
        let error = CocoError::model_provider_validation("Invalid input");
        let (status, response) = map_error_to_response(&error);

        assert_eq!(status, StatusCode::BAD_REQUEST);

        let response_value: serde_json::Value = response;
        assert_eq!(response_value["error"], "validation_error");
        assert_eq!(response_value["message"], "Invalid input");
    }

    #[test]
    fn test_map_not_found_error() {
        let error = CocoError::model_provider_not_found("test-id");
        let (status, response) = map_error_to_response(&error);

        assert_eq!(status, StatusCode::NOT_FOUND);

        let response_value: serde_json::Value = response;
        assert_eq!(response_value["error"], "not_found");
    }

    #[test]
    fn test_map_builtin_protection_error() {
        let error = CocoError::builtin_provider_protection("Cannot delete builtin provider");
        let (status, response) = map_error_to_response(&error);

        assert_eq!(status, StatusCode::FORBIDDEN);

        let response_value: serde_json::Value = response;
        assert_eq!(response_value["error"], "permission_denied");
    }

    #[test]
    fn test_create_test_request() {
        let request = create_test_request();
        assert_eq!(request.name, "Test Provider");
        assert_eq!(request.api_type, "openai");
        assert!(request.enabled);
    }

    #[test]
    fn test_update_request_validation() {
        use crate::models::model_provider::UpdateModelProviderRequest;

        let update_request = UpdateModelProviderRequest {
            name: Some("Updated Provider".to_string()),
            api_key: None,
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: Some(false),
            description: Some("Updated description".to_string()),
        };

        // 基本验证测试
        assert_eq!(update_request.name.as_ref().unwrap(), "Updated Provider");
        assert_eq!(update_request.enabled.unwrap(), false);
    }

    #[test]
    fn test_search_request_creation() {
        let search_request = SearchRequest {
            size: Some(10),
            from: Some(0),
            sort: Some("created:desc".to_string()),
            query: Some(SearchQuery {
                text: Some("deepseek".to_string()),
                filters: Some({
                    let mut filters = std::collections::HashMap::new();
                    filters.insert("enabled".to_string(), serde_json::Value::Bool(true));
                    filters
                }),
            }),
        };

        assert_eq!(search_request.size, Some(10));
        assert_eq!(search_request.from, Some(0));
        assert_eq!(search_request.sort, Some("created:desc".to_string()));
        assert!(search_request.query.is_some());

        let query = search_request.query.unwrap();
        assert_eq!(query.text, Some("deepseek".to_string()));
        assert!(query.filters.is_some());
    }

    #[test]
    fn test_convert_search_params_to_query() {
        let mut search_params = SearchParams::new()
            .with_query("test".to_string())
            .with_pagination(20, 10)
            .with_sort("name:asc".to_string());

        search_params
            .filters
            .insert("enabled".to_string(), serde_json::Value::Bool(true));

        let search_query = convert_search_params_to_query(search_params);

        assert_eq!(search_query.get_size(), 20);
        assert_eq!(search_query.get_from(), 10);
        assert_eq!(search_query.q, Some("test".to_string()));
        assert_eq!(search_query.sort, Some("name:asc".to_string()));
        assert!(search_query.filters.is_some());
    }
}
